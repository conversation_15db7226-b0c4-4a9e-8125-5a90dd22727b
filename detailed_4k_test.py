#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对可用接口进行详细的4K内容检测
"""

import requests
import json
import time
from datetime import datetime
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_4k_content(name, url):
    """详细测试4K内容"""
    print(f"\n{'='*60}")
    print(f"详细测试: {name}")
    print(f"URL: {url}")
    print(f"{'='*60}")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
    })
    
    try:
        # 获取列表数据
        list_url = f"{url}?ac=list&pg=1"
        print(f"获取列表: {list_url}")
        
        response = session.get(list_url, timeout=15, verify=False)
        if response.status_code != 200:
            print(f"❌ 无法获取列表数据，状态码: {response.status_code}")
            return
            
        try:
            data = response.json()
        except:
            print("⚠️ 响应不是JSON格式，尝试解析XML或其他格式")
            # 检查是否是XML格式
            if '<?xml' in response.text[:100]:
                print("📄 检测到XML格式响应")
                # 简单的XML解析来查找4K关键词
                content = response.text.upper()
                if any(keyword in content for keyword in ['4K', 'UHD', 'HDR', '2160P', '超清', '蓝光']):
                    print("🎬 在XML响应中发现4K相关关键词")
                else:
                    print("📺 在XML响应中未发现4K关键词")
            return
            
        print(f"📊 总影片数量: {data.get('total', 0)}")
        
        # 检查前几页的内容
        found_4k = []
        for page in range(1, 4):  # 检查前3页
            try:
                page_url = f"{url}?ac=list&pg={page}"
                page_response = session.get(page_url, timeout=10, verify=False)
                page_data = page_response.json()
                
                if 'list' in page_data:
                    for video in page_data['list']:
                        if isinstance(video, dict):
                            name_field = video.get('vod_name', '')
                            remarks = video.get('vod_remarks', '')
                            
                            # 检查4K关键词
                            combined_text = f"{name_field} {remarks}".upper()
                            if any(keyword in combined_text for keyword in ['4K', 'UHD', 'HDR', '2160P', '超清', '蓝光']):
                                found_4k.append({
                                    'name': name_field,
                                    'remarks': remarks,
                                    'id': video.get('vod_id', ''),
                                    'type': video.get('type_name', '')
                                })
                                
                print(f"第{page}页检查完成，发现{len([v for v in found_4k if v not in found_4k[:-len(page_data.get('list', []))]])}个4K内容")
                time.sleep(0.5)  # 避免请求过快
                
            except Exception as e:
                print(f"检查第{page}页时出错: {e}")
                break
                
        if found_4k:
            print(f"\n🎬 发现 {len(found_4k)} 个4K相关内容:")
            for i, video in enumerate(found_4k[:10], 1):  # 显示前10个
                print(f"  {i}. {video['name']} ({video['remarks']}) - ID: {video['id']}")
                
            # 测试一个具体的4K视频详情
            if found_4k:
                test_video = found_4k[0]
                detail_url = f"{url}?ac=detail&ids={test_video['id']}"
                print(f"\n🔍 测试详情接口: {detail_url}")
                
                detail_response = session.get(detail_url, timeout=10, verify=False)
                if detail_response.status_code == 200:
                    try:
                        detail_data = detail_response.json()
                        if 'list' in detail_data and detail_data['list']:
                            video_detail = detail_data['list'][0]
                            play_url = video_detail.get('vod_play_url', '')
                            if play_url:
                                print(f"✅ 获取到播放地址，长度: {len(play_url)} 字符")
                                # 检查播放源数量
                                play_sources = play_url.split('$$$') if '$$$' in play_url else [play_url]
                                print(f"📺 播放源数量: {len(play_sources)}")
                            else:
                                print("⚠️ 未获取到播放地址")
                    except:
                        print("⚠️ 详情响应解析失败")
                else:
                    print(f"❌ 详情接口请求失败: {detail_response.status_code}")
        else:
            print("📺 未发现明确的4K内容标识")
            
        # 搜索特定4K关键词
        print(f"\n🔍 搜索4K关键词...")
        search_keywords = ['4K', 'UHD', 'HDR']
        for keyword in search_keywords:
            try:
                search_url = f"{url}?ac=list&wd={keyword}"
                search_response = session.get(search_url, timeout=10, verify=False)
                if search_response.status_code == 200:
                    search_data = search_response.json()
                    count = search_data.get('total', 0)
                    print(f"  关键词 '{keyword}': {count} 个结果")
                time.sleep(0.5)
            except Exception as e:
                print(f"  关键词 '{keyword}' 搜索失败: {e}")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🎬 4K播放源详细内容检测")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    # 只测试可用的接口
    available_sources = [
        ("4K屋资源", "https://www.4khouse.com/api.php/provide/vod"),
        ("闪电资源", "https://sdzyapi.com/api.php/provide/vod"),
        ("量子资源", "https://cj.lziapi.com/api.php/provide/vod"),
    ]
    
    for name, url in available_sources:
        test_4k_content(name, url)
        time.sleep(2)  # 避免请求过于频繁
        
    print("\n" + "="*80)
    print("📊 详细测试完成")
    print("="*80)

if __name__ == "__main__":
    main()
