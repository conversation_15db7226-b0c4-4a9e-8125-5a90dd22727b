import { ArrowLeft } from 'lucide-react';

export function BackButton() {
  return (
    <button
      onClick={() => window.history.back()}
      className='w-10 h-10 p-2 rounded-xl flex items-center justify-center text-gray-600 hover:bg-gradient-to-r hover:from-gray-200/60 hover:to-gray-100/40 dark:text-gray-300 dark:hover:from-gray-700/60 dark:hover:to-gray-600/40 transition-all duration-300 hover:scale-110 hover:shadow-lg active:scale-95'
      aria-label='Back'
    >
      <ArrowLeft className='w-full h-full transition-transform duration-300 hover:-translate-x-1' />
    </button>
  );
}
