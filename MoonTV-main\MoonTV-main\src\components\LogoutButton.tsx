/* eslint-disable @typescript-eslint/no-explicit-any */

'use client';

import { LogOut } from 'lucide-react';

/**
 * 退出登录按钮
 *
 * 功能：
 * 1. 清除 localStorage 中保存的 username 和 password
 * 2. 跳转到 /login 页面
 */
export function LogoutButton() {
  const handleLogout = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('username');
      localStorage.removeItem('password');
    }
    // 使用 replace，避免用户返回上一页时仍然处于已登录状态
    window.location.reload();
  };

  return (
    <button
      onClick={handleLogout}
      className='w-10 h-10 p-2 rounded-xl flex items-center justify-center text-gray-600 hover:bg-gradient-to-r hover:from-red-100/60 hover:to-red-50/40 hover:text-red-600 dark:text-gray-300 dark:hover:from-red-900/40 dark:hover:to-red-800/20 dark:hover:text-red-400 transition-all duration-300 hover:scale-110 hover:shadow-lg active:scale-95'
      aria-label='Logout'
    >
      <LogOut className='w-full h-full transition-transform duration-300 hover:rotate-12' />
    </button>
  );
}
