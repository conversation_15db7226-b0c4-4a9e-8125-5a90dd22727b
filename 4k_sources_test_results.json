{"test_time": "2025-07-08T13:31:16.111596", "total_sources": 12, "available_sources": 3, "availability_rate": "25.0%", "results": {"4K屋资源": {"list_status": 200, "list_response_time": "947.85ms", "list_accessible": true, "list_data_format": "Non-JSON", "detail_status": 200, "detail_response_time": "226.0ms", "detail_accessible": true}, "蓝光资源": {"list_status": "CONNECTION_ERROR", "list_accessible": false, "detail_status": "ERROR: HTTPSConnectionPool(host='api.languang.com', port=443): Max retries exceeded with url: /api.php/provide/vod?ac=detail&ids=1 (Caused by ProxyError('Cannot connect to proxy.', FileNotFoundError(2, 'No such file or directory')))", "detail_accessible": false}, "超清资源": {"list_status": "CONNECTION_ERROR", "list_accessible": false, "detail_status": "ERROR: HTTPSConnectionPool(host='cqzy.cc', port=443): Max retries exceeded with url: /api.php/provide/vod?ac=detail&ids=1 (Caused by ProxyError('Cannot connect to proxy.', FileNotFoundError(2, 'No such file or directory')))", "detail_accessible": false}, "HDR资源": {"list_status": "CONNECTION_ERROR", "list_accessible": false, "detail_status": "ERROR: HTTPSConnectionPool(host='hdrapi.com', port=443): Max retries exceeded with url: /api.php/provide/vod?ac=detail&ids=1 (Caused by ProxyError('Cannot connect to proxy.', FileNotFoundError(2, 'No such file or directory')))", "detail_accessible": false}, "极清资源": {"list_status": 404, "list_response_time": "799.01ms", "list_accessible": false, "detail_status": 404, "detail_response_time": "109.8ms", "detail_accessible": false}, "原画资源": {"list_status": "CONNECTION_ERROR", "list_accessible": false, "detail_status": "ERROR: HTTPSConnectionPool(host='yuanhua.tv', port=443): Max retries exceeded with url: /api.php/provide/vod?ac=detail&ids=1 (Caused by ProxyError('Cannot connect to proxy.', FileNotFoundError(2, 'No such file or directory')))", "detail_accessible": false}, "UHD资源": {"list_status": "CONNECTION_ERROR", "list_accessible": false, "detail_status": "ERROR: HTTPSConnectionPool(host='uhdapi.com', port=443): Max retries exceeded with url: /api.php/provide/vod?ac=detail&ids=1 (Caused by ProxyError('Cannot connect to proxy.', FileNotFoundError(2, 'No such file or directory')))", "detail_accessible": false}, "蓝光4K": {"list_status": "CONNECTION_ERROR", "list_accessible": false, "detail_status": "ERROR: HTTPSConnectionPool(host='4kbdapi.com', port=443): Max retries exceeded with url: /api.php/provide/vod?ac=detail&ids=1 (Caused by ProxyError('Cannot connect to proxy.', FileNotFoundError(2, 'No such file or directory')))", "detail_accessible": false}, "飞速资源": {"list_status": "CONNECTION_ERROR", "list_accessible": false, "detail_status": "ERROR: HTTPSConnectionPool(host='www.feisuzy.com', port=443): Max retries exceeded with url: /api.php/provide/vod?ac=detail&ids=1 (Caused by ProxyError('Cannot connect to proxy.', TimeoutError('_ssl.c:980: The handshake operation timed out')))", "detail_accessible": false}, "闪电资源": {"list_status": 200, "list_response_time": "514.67ms", "list_accessible": true, "list_data_format": "JSON", "list_total_count": 97949, "has_4k_content": false, "detail_status": 200, "detail_response_time": "356.46ms", "detail_accessible": true}, "量子资源": {"list_status": 200, "list_response_time": "828.87ms", "list_accessible": true, "list_data_format": "JSON", "list_total_count": 105506, "has_4k_content": false, "detail_status": 200, "detail_response_time": "416.33ms", "detail_accessible": true}, "天空资源": {"list_status": "CONNECTION_ERROR", "list_accessible": false, "detail_status": "ERROR: HTTPSConnectionPool(host='api.tiankongapi.com', port=443): Max retries exceeded with url: /api.php/provide/vod?ac=detail&ids=1 (Caused by ProxyError('Cannot connect to proxy.', FileNotFoundError(2, 'No such file or directory')))", "detail_accessible": false}}}