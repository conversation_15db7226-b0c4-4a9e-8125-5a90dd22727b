#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
4K播放源接口可用性测试脚本
测试各个4K视频资源API接口的连通性和响应状态
"""

import requests
import json
import time
from datetime import datetime
import urllib3
from urllib.parse import urljoin

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class APITester:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        })
        self.timeout = 10
        
    def test_api(self, name, url, description):
        """测试单个API接口"""
        print(f"\n{'='*60}")
        print(f"测试接口: {name}")
        print(f"描述: {description}")
        print(f"URL: {url}")
        print(f"{'='*60}")
        
        results = {}
        
        # 测试基础连通性
        try:
            # 测试列表接口
            list_url = f"{url}?ac=list"
            print(f"测试列表接口: {list_url}")
            
            start_time = time.time()
            response = self.session.get(list_url, timeout=self.timeout, verify=False)
            response_time = round((time.time() - start_time) * 1000, 2)
            
            results['list_status'] = response.status_code
            results['list_response_time'] = f"{response_time}ms"
            results['list_accessible'] = response.status_code == 200
            
            if response.status_code == 200:
                print(f"✅ 列表接口可访问 - 状态码: {response.status_code}, 响应时间: {response_time}ms")
                
                # 尝试解析JSON响应
                try:
                    data = response.json()
                    results['list_data_format'] = 'JSON'
                    results['list_total_count'] = data.get('total', 0) if isinstance(data, dict) else len(data)
                    print(f"📊 数据格式: JSON, 总数: {results['list_total_count']}")
                    
                    # 检查是否有4K相关内容
                    if isinstance(data, dict) and 'list' in data:
                        video_list = data['list'][:5]  # 检查前5个
                        for video in video_list:
                            if isinstance(video, dict):
                                name_field = video.get('vod_name', '')
                                if any(keyword in name_field.upper() for keyword in ['4K', 'UHD', 'HDR', '2160P']):
                                    results['has_4k_content'] = True
                                    print(f"🎬 发现4K内容: {name_field}")
                                    break
                        else:
                            results['has_4k_content'] = False
                            
                except json.JSONDecodeError:
                    results['list_data_format'] = 'Non-JSON'
                    print("⚠️ 响应不是有效的JSON格式")
                    
            else:
                print(f"❌ 列表接口不可访问 - 状态码: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"⏰ 请求超时 (>{self.timeout}秒)")
            results['list_status'] = 'TIMEOUT'
            results['list_accessible'] = False
            
        except requests.exceptions.ConnectionError:
            print("🔌 连接错误 - 无法连接到服务器")
            results['list_status'] = 'CONNECTION_ERROR'
            results['list_accessible'] = False
            
        except Exception as e:
            print(f"❌ 其他错误: {str(e)}")
            results['list_status'] = f'ERROR: {str(e)}'
            results['list_accessible'] = False
            
        # 测试详情接口
        try:
            detail_url = f"{url}?ac=detail&ids=1"
            print(f"\n测试详情接口: {detail_url}")
            
            start_time = time.time()
            response = self.session.get(detail_url, timeout=self.timeout, verify=False)
            response_time = round((time.time() - start_time) * 1000, 2)
            
            results['detail_status'] = response.status_code
            results['detail_response_time'] = f"{response_time}ms"
            results['detail_accessible'] = response.status_code == 200
            
            if response.status_code == 200:
                print(f"✅ 详情接口可访问 - 状态码: {response.status_code}, 响应时间: {response_time}ms")
            else:
                print(f"❌ 详情接口不可访问 - 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 详情接口测试失败: {str(e)}")
            results['detail_status'] = f'ERROR: {str(e)}'
            results['detail_accessible'] = False
            
        return results

def main():
    """主函数"""
    print("🎬 4K播放源接口可用性测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    # 4K专门资源站
    sources_4k = [
        ("4K屋资源", "https://www.4khouse.com/api.php/provide/vod", "专注4K超高清内容"),
        ("蓝光资源", "https://api.languang.com/api.php/provide/vod", "提供蓝光原盘和4K重制版"),
        ("超清资源", "https://cqzy.cc/api.php/provide/vod", "4K和8K内容为主"),
        ("HDR资源", "https://hdrapi.com/api.php/provide/vod", "支持HDR和4K HDR"),
        ("极清资源", "https://jqapi.com/api.php/provide/vod", "4K/8K超高清专站"),
        ("原画资源", "https://yuanhua.tv/api.php/provide/vod", "原画质量，包含4K"),
        ("UHD资源", "https://uhdapi.com/api.php/provide/vod", "Ultra HD 4K专门站"),
        ("蓝光4K", "https://4kbdapi.com/api.php/provide/vod", "蓝光4K影片"),
    ]
    
    # 部分支持4K的综合资源站
    sources_mixed = [
        ("飞速资源", "https://www.feisuzy.com/api.php/provide/vod", "部分新片提供4K版本"),
        ("闪电资源", "https://sdzyapi.com/api.php/provide/vod", "主打高清，包含4K片源"),
        ("量子资源", "https://cj.lziapi.com/api.php/provide/vod", "新片通常有4K版本"),
        ("天空资源", "https://api.tiankongapi.com/api.php/provide/vod", "热门影片提供4K选项"),
    ]
    
    tester = APITester()
    all_results = {}
    
    print("\n🎯 测试4K专门资源站:")
    for name, url, desc in sources_4k:
        results = tester.test_api(name, url, desc)
        all_results[name] = results
        time.sleep(1)  # 避免请求过于频繁
        
    print("\n\n🎯 测试部分支持4K的综合资源站:")
    for name, url, desc in sources_mixed:
        results = tester.test_api(name, url, desc)
        all_results[name] = results
        time.sleep(1)  # 避免请求过于频繁
        
    # 生成测试报告
    print("\n" + "="*80)
    print("📊 测试结果汇总")
    print("="*80)
    
    available_count = 0
    total_count = len(all_results)
    
    for name, results in all_results.items():
        status = "✅ 可用" if results.get('list_accessible', False) else "❌ 不可用"
        if results.get('list_accessible', False):
            available_count += 1
            
        response_time = results.get('list_response_time', 'N/A')
        has_4k = "🎬 有4K内容" if results.get('has_4k_content', False) else "📺 未检测到4K"
        
        print(f"{name:12} | {status:8} | 响应时间: {response_time:8} | {has_4k}")
        
    print(f"\n📈 总体可用率: {available_count}/{total_count} ({available_count/total_count*100:.1f}%)")
    
    # 保存详细结果到JSON文件
    with open('4k_sources_test_results.json', 'w', encoding='utf-8') as f:
        json.dump({
            'test_time': datetime.now().isoformat(),
            'total_sources': total_count,
            'available_sources': available_count,
            'availability_rate': f"{available_count/total_count*100:.1f}%",
            'results': all_results
        }, f, ensure_ascii=False, indent=2)
        
    print(f"\n💾 详细测试结果已保存到: 4k_sources_test_results.json")

if __name__ == "__main__":
    main()
